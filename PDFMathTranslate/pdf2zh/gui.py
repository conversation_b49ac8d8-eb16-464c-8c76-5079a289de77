import os
import shutil
import tempfile
from pathlib import Path
from pdf2zh import __version__
from pdf2zh.high_level import translate
from pdf2zh.translator import (
    BaseTranslator,
    GoogleTranslator,
    BingTranslator,
    DeepLTranslator,
    DeepLXTranslator,
    OllamaTranslator,
    AzureOpenAITranslator,
    OpenAITranslator,
    ZhipuTranslator,
    SiliconTranslator,
    AzureTranslator,
    TencentTranslator,
    GoogleGeminiTranslator,
)

# 尝试导入Xinference支持
try:
    from pdf2zh.xinference_support import XinferenceTranslator, check_xinference_available
    xinference_available = check_xinference_available()
except ImportError:
    xinference_available = False

import gradio as gr
import numpy as np
import pymupdf
import tqdm
import requests
import cgi

service_map: dict[str, BaseTranslator] = {
    "Google": GoogleTranslator,
    "Bing": BingTranslator,
    "DeepL": DeepLTranslator,
    "DeepLX": DeepLXTranslator,
    "Ollama": OllamaTranslator,
    "AzureOpenAI": AzureOpenAITranslator,
    "OpenAI": OpenAITranslator,
    "Zhipu": ZhipuTranslator,
    "Silicon": SiliconTranslator,
    "Azure": AzureTranslator,
    "Tencent": TencentTranslator,
    "Gemini": GoogleGeminiTranslator,
    "BabelDOC": GoogleTranslator,  # BabelDOC使用Google作为默认翻译器
}

# 如果Xinference可用，添加到服务映射中
if xinference_available:
    # 创建一个简单的Xinference翻译器类，继承自BaseTranslator
    class XinferenceTranslatorWrapper(BaseTranslator):
        name = "Xinference"
        envs = {
            "XINFERENCE_ENDPOINT": "http://localhost:9997",
            "XINFERENCE_MODEL": "llama3",
            "XINFERENCE_API_KEY": "",
        }

        def __init__(self, **kwargs):
            super().__init__(**kwargs)

    # 添加到服务映射
    service_map["Xinference"] = XinferenceTranslatorWrapper
lang_map = {
    "Chinese": "zh",
    "English": "en",
    "French": "fr",
    "German": "de",
    "Japanese": "ja",
    "Korean": "ko",
    "Russian": "ru",
    "Spanish": "es",
    "Italian": "it",
}
page_map = {
    "All": None,
    "First": [0],
    "First 5 pages": list(range(0, 5)),
}

flag_demo = False
if os.getenv("PDF2ZH_DEMO"):
    flag_demo = True
    service_map = {
        "Google": GoogleTranslator,
    }
    page_map = {
        "First": [0],
        "First 20 pages": list(range(0, 20)),
    }
    client_key = os.getenv("PDF2ZH_CLIENT_KEY")
    server_key = os.getenv("PDF2ZH_SERVER_KEY")

# 获取隐藏服务列表
hidden_services = os.getenv("HIDDEN_SERVICES", "").split(",")
hidden_services = [s.strip() for s in hidden_services if s.strip()]

# 过滤服务映射，移除隐藏的服务
filtered_service_map = {k: v for k, v in service_map.items() if k not in hidden_services}

# 如果过滤后没有服务，则使用原始服务映射
if not filtered_service_map:
    filtered_service_map = service_map
    print("警告：所有服务都被隐藏，将显示所有服务")


def verify_recaptcha(response):
    recaptcha_url = "https://www.google.com/recaptcha/api/siteverify"
    print("reCAPTCHA", server_key, response)
    data = {"secret": server_key, "response": response}
    result = requests.post(recaptcha_url, data=data).json()
    print("reCAPTCHA", result.get("success"))
    return result.get("success")


def clean_cache():
    """清理翻译缓存"""
    cache_dir = os.path.join(tempfile.gettempdir(), "cache")

    if os.path.exists(cache_dir):
        try:
            shutil.rmtree(cache_dir)
            return "缓存已成功清理！"
        except Exception as e:
            return f"清理缓存时出错: {e}"
    else:
        return "缓存目录不存在，无需清理"

def file_preview(file):
    try:
        # 检查文件是否存在
        if not os.path.exists(file):
            raise gr.Error("文件不存在")

        file_ext = os.path.splitext(file)[1].lower()
        if file_ext == '.pdf':
            try:
                doc = pymupdf.open(file)
                page = doc[0]
                pix = page.get_pixmap()
                image = np.frombuffer(pix.samples, np.uint8).reshape(pix.height, pix.width, 3)
                doc.close()
                return image
            except Exception as e:
                raise gr.Error(f"PDF文件预览失败: {str(e)}")
        elif file_ext in ['.txt', '.md', '.rtf']:
            try:
                # 直接读取文件内容，限制读取大小
                content = ''
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read(2000)  # 减少预览字符数
                except UnicodeDecodeError:
                    with open(file, 'r', encoding='gbk') as f:
                        content = f.read(2000)  # 减少预览字符数
                except Exception as e:
                    raise gr.Error(f"文本文件读取失败: {str(e)}")

                if len(content) >= 2000:
                    content = content[:1997] + '...'
                # 返回处理后的文本内容
                return content
            except Exception as e:
                raise gr.Error(f"文本文件预览失败: {str(e)}")
        else:
            raise gr.Error(f"不支持的文件类型: {file_ext}")
    except Exception as e:
        raise gr.Error(f"文件预览失败: {str(e)}")


def upload_pdf_file(file, service, progress=gr.Progress()):
    if not file:
        return None, None

    file_ext = os.path.splitext(file)[1].lower()
    if file_ext != '.pdf':
        raise gr.Error("Invalid file type. Only PDF files are allowed.")

    preview_content = file_preview(file)
    return file, preview_content

def upload_text_file(file, service, progress=gr.Progress()):
    if not file:
        return None

    file_ext = os.path.splitext(file)[1].lower()
    if file_ext not in ['.txt', '.md', '.rtf', '.docx', '.epub']:
        raise gr.Error("Invalid file type. Only .txt, .md, .rtf, .docx, .epub files are allowed.")

    return file


def download_with_limit(url, save_path, size_limit):
    chunk_size = 1024
    total_size = 0
    with requests.get(url, stream=True, timeout=10) as response:
        response.raise_for_status()
        content = response.headers.get("Content-Disposition")
        try:  # filename from header
            _, params = cgi.parse_header(content)
            filename = params["filename"]
        except Exception:  # filename from url
            filename = os.path.basename(url)
        with open(save_path / filename, "wb") as file:
            for chunk in response.iter_content(chunk_size=chunk_size):
                total_size += len(chunk)
                if size_limit and total_size > size_limit:
                    raise gr.Error("Exceeds file size limit")
                file.write(chunk)
    return save_path / filename


def translate_file(
    file_type,
    file_input,
    link_input,
    service,
    lang_from,
    lang_to,
    page_range,
    recaptcha_response,
    model=None,  # 添加模型参数
    progress=gr.Progress(),
    compatible_mode=False,  # 添加兼容模式参数
    *envs,
):
    """Translate PDF or text file content using selected service."""
    if flag_demo and not verify_recaptcha(recaptcha_response):
        raise gr.Error("reCAPTCHA验证失败")

    output = Path("pdf2zh_files")
    output.mkdir(parents=True, exist_ok=True)

    # 获取输入文件
    if file_type == "File":
        if not file_input:
            raise gr.Error("请选择要翻译的文件")
        file_path = shutil.copy(file_input, output)
    else:
        if not link_input:
            raise gr.Error("请输入文件链接")
        file_path = download_with_limit(
            link_input,
            output,
            5 * 1024 * 1024 if flag_demo else None,
        )

    # 处理文件名，确保不会过长
    filename = os.path.splitext(os.path.basename(file_path))[0]
    if len(filename) > 150:  # 限制基本文件名长度
        filename = filename[:147] + '...'
    file_ext = os.path.splitext(file_path)[1].lower()

    # 验证文件类型
    if file_ext == '.pdf':
        file_type = 'pdf'
    elif file_ext in ['.txt', '.md', '.rtf', '.docx', '.epub']:
        file_type = 'text'
    else:
        raise gr.Error(f"不支持的文件类型: {file_ext}，仅支持PDF、TXT、MD、RTF、DOCX和EPUB文件")

    if file_ext in ['.txt', '.md', '.docx', '.epub']:
        file_en = output / f"{filename}{file_ext}"
        file_zh = output / f"{filename}-zh{file_ext}"
        file_dual = output / f"{filename}-dual{file_ext}"
    else:  # PDF files
        file_en = output / f"{filename}.pdf"
        file_zh = output / f"{filename}-zh.pdf"
        file_dual = output / f"{filename}-dual.pdf"

    translator = filtered_service_map[service]
    selected_page = page_map[page_range]
    lang_from = lang_map[lang_from]
    lang_to = lang_map[lang_to]

    # 设置模型
    print(f"模型参数: {model}, 类型: {type(model)}")
    if model and isinstance(model, str) and model.strip():
        if service == "Ollama":
            os.environ["OLLAMA_MODEL"] = model
            print(f"设置Ollama模型: {model}")
            # 确保使用正确的模型
            if "qwen" in model.lower():
                # 对于Qwen模型，确保使用/no_think标记
                os.environ["OLLAMA_THINKING_MODEL"] = "true"
                os.environ["OLLAMA_USE_NO_THINK"] = "true"
                os.environ["OLLAMA_USE_TWO_PHASE"] = "false"
                print(f"检测到Qwen模型，启用/no_think标记")
        elif service == "Gemini":
            os.environ["GOOGLE_GENAI_MODEL"] = model
            print(f"设置Gemini模型: {model}")

    # 设置环境变量
    if envs:
        # 如果是 Gemini 服务，特殊处理
        if service == "Gemini":
            # 设置 API Key
            if envs[0]:
                os.environ["GOOGLE_API_KEY"] = envs[0]

            # 设置是否显示思考过程
            if isinstance(envs[1], bool):
                os.environ["GOOGLE_GENAI_THINKING"] = "true" if envs[1] else "false"
                print(f"Setting GOOGLE_GENAI_THINKING to {os.environ['GOOGLE_GENAI_THINKING']}")
        # 如果是 Ollama 服务，特殊处理
        elif service == "Ollama":
            # 设置环境变量
            for i, env in enumerate(translator.envs.items()):
                if i < len(envs) and envs[i]:
                    # 如果是布尔值，转换为字符串
                    if isinstance(envs[i], bool):
                        os.environ[env[0]] = "true" if envs[i] else "false"
                    else:
                        os.environ[env[0]] = envs[i]

            # 特殊处理新增的环境变量
            if "OLLAMA_THINKING_MODEL" in translator.envs:
                os.environ["OLLAMA_THINKING_MODEL"] = "true"  # 默认为思考型模型
            if "OLLAMA_USE_NO_THINK" in translator.envs:
                os.environ["OLLAMA_USE_NO_THINK"] = "true"    # 默认使用 /no_think 标记
            if "OLLAMA_USE_TWO_PHASE" in translator.envs:
                os.environ["OLLAMA_USE_TWO_PHASE"] = "false"  # 默认不使用两阶段处理
        # 其他服务正常处理
        else:
            for i, env in enumerate(translator.envs.items()):
                if i < len(envs) and envs[i]:
                    # 如果是布尔值，转换为字符串
                    if isinstance(envs[i], bool):
                        os.environ[env[0]] = "true" if envs[i] else "false"
                    else:
                        os.environ[env[0]] = envs[i]

    print(f"Files before translation: {os.listdir(output)}")

    def progress_bar(t: tqdm.tqdm):
        progress_value = t.n / t.total
        if file_type == 'text':
            progress(progress_value, desc=f"Translating Text File...")
            return progress_value
        else:
            progress(progress_value, desc=f"Translating PDF File...")
            return progress_value

    param = {
        "files": [file_en],
        "pages": selected_page,
        "lang_in": lang_from,
        "lang_out": lang_to,
        "service": f"{translator.name}",
        "output": output,
        "thread": 4,
        "callback": progress_bar,
        "compatible": compatible_mode,  # 添加兼容模式参数
    }
    print(param)

    # 检查是否使用特殊后端
    if service == "BabelDOC":
        try:
            from pdf2zh.babeldoc_support import translate_with_babeldoc, check_babeldoc_available, install_babeldoc

            # 检查BabelDOC是否可用
            if not check_babeldoc_available():
                if not install_babeldoc():
                    raise gr.Error("BabelDOC后端不可用，请手动安装: pip install babeldoc>=0.1.22,<0.3.0")

            # 使用BabelDOC翻译
            progress(0.3, desc="使用BabelDOC翻译...")
            file_zh, file_dual = translate_with_babeldoc(
                file_en,
                output,
                lang_from,
                lang_to,
                translator.name.lower()
            )
            progress(1.0, desc="BabelDOC翻译完成!")
        except Exception as e:
            raise gr.Error(f"BabelDOC翻译失败: {str(e)}")
    elif service == "Xinference":
        try:
            from pdf2zh.xinference_support import XinferenceTranslator, check_xinference_available, install_xinference

            # 检查Xinference是否可用
            if not check_xinference_available():
                if not install_xinference():
                    raise gr.Error("Xinference不可用，请手动安装: pip install xinference")

            # 获取环境变量
            endpoint = os.getenv("XINFERENCE_ENDPOINT", "http://localhost:9997")
            model_name = os.getenv("XINFERENCE_MODEL", "llama3")
            api_key = os.getenv("XINFERENCE_API_KEY", "")

            # 使用标准翻译，但设置特殊参数
            progress(0.1, desc="使用Xinference翻译...")

            # 创建Xinference翻译器
            xinference_translator = XinferenceTranslator(
                model_name=model_name,
                endpoint=endpoint,
                lang_in=lang_from,
                lang_out=lang_to,
                api_key=api_key
            )

            # 修改参数以使用Xinference翻译器
            param["service"] = "custom"
            param["custom_translator"] = xinference_translator

            # 使用标准翻译流程
            translate(**param)

            progress(1.0, desc="Xinference翻译完成!")
        except Exception as e:
            raise gr.Error(f"Xinference翻译失败: {str(e)}")
    else:
        # 使用标准翻译
        translate(**param)

    print(f"Files after translation: {os.listdir(output)}")

    if not file_zh.exists():
        raise gr.Error("No output")

    try:
        if file_ext in ['.txt', '.md', '.rtf', '.docx', '.epub']:
            try:
                progress(1.0, desc="Translation complete!")
                return (
                    str(file_zh),
                    None,  # 文本文件不需要预览图像
                    None,  # 文本文件不生成双语文件
                    gr.update(visible=True),
                    gr.update(visible=True),
                    gr.update(visible=False),  # 隐藏双语文件下载按钮
                )
            except Exception as e:
                raise gr.Error(f"翻译失败: {str(e)}")
        else:  # PDF files
            doc = pymupdf.open(str(file_zh))
            page = doc[0]
            pix = page.get_pixmap()
            translated_preview = np.frombuffer(pix.samples, np.uint8).reshape(pix.height, pix.width, 3)
            doc.close()
            progress(1.0, desc="Translation complete!")
            return (
                str(file_zh),
                translated_preview,
                str(file_dual),
                gr.update(visible=True),
                gr.update(visible=True),
                gr.update(visible=True),
            )
    except Exception as e:
        raise gr.Error(f"预览失败: {str(e)}")


# Global setup
custom_blue = gr.themes.Color(
    c50="#E8F3FF",
    c100="#BEDAFF",
    c200="#94BFFF",
    c300="#6AA1FF",
    c400="#4080FF",
    c500="#165DFF",  # Primary color
    c600="#0E42D2",
    c700="#0A2BA6",
    c800="#061D79",
    c900="#03114D",
    c950="#020B33",
)

def launch_gui(initial_port=7860):
    """启动GUI界面，如果端口被占用则尝试其他端口"""
    max_attempts = 10
    current_port = initial_port

    demo = gr.Blocks(
        title="PDFMathTranslate - PDF Translation with preserved formats",
        theme=gr.themes.Default(
            primary_hue=custom_blue,
            spacing_size="md",
            radius_size="lg"
        ),
        css="""
    .secondary-text {color: #999 !important;}
    footer {visibility: hidden}
    .env-warning {color: #dd5500 !important;}
    .env-success {color: #559900 !important;}

    /* Add dashed border to input-file class */
    .input-file {
        border: 1.2px dashed #165DFF !important;
        border-radius: 6px !important;
        /* background-color: #ffffff !important; */
        transition: background-color 0.4s ease-out;
    }

    .input-file:hover {
        border: 1.2px dashed #165DFF !important;
        border-radius: 6px !important;
        color: #165DFF !important;
        background-color: #E8F3FF !important;
        transition: background-color 0.2s ease-in;
    }

    .progress-bar-wrap {
    border-radius: 8px !important;
    }
    .progress-bar {
    border-radius: 8px !important;
    }
    """,
        head="""<script src="https://www.google.com/recaptcha/api.js?render=explicit" async defer></script>
    <script type="text/javascript">
        var onVerify = function(token) {
            el=document.getElementById('verify').getElementsByTagName('textarea')[0];
            el.value=token;
            el.dispatchEvent(new Event('input'));
        };
    </script>
    """ if flag_demo else ""
    )

    with demo:
        with gr.Row():
            gr.Markdown(
                "# [PDFMathTranslate @ GitHub](https://github.com/Byaidu/PDFMathTranslate)"
            )

            # 添加清除缓存按钮
            clear_cache_btn = gr.Button("清除翻译缓存", variant="secondary")
            cache_status = gr.Textbox(label="缓存状态", interactive=False)

            # 绑定清除缓存按钮事件
            clear_cache_btn.click(
                fn=clean_cache,
                inputs=[],
                outputs=[cache_status],
            )


        with gr.Row():
            # PDF处理区域
            with gr.Column(scale=1):
                gr.Markdown("## PDF文件翻译")
                pdf_file_type = gr.Radio(
                    choices=["File", "Link"],
                    label="上传方式",
                    value="File",
                )
                pdf_file_input = gr.File(
                    label="选择PDF文件",
                    file_count="single",
                    file_types=[".pdf"],
                    type="filepath",
                    elem_classes=["input-file"],
                )
                pdf_link_input = gr.Textbox(
                    label="PDF链接",
                    visible=False,
                    interactive=True,
                )
                pdf_preview = gr.Image(label="PDF预览", interactive=False)
                gr.Markdown("### 翻译选项")
                with gr.Row():
                    pdf_lang_from = gr.Dropdown(
                        label="源语言",
                        choices=lang_map.keys(),
                        value="English",
                    )
                    pdf_lang_to = gr.Dropdown(
                        label="目标语言",
                        choices=lang_map.keys(),
                        value="Chinese",
                    )
                pdf_service = gr.Dropdown(
                    label="翻译服务",
                    choices=filtered_service_map.keys(),
                    value=next(iter(filtered_service_map.keys())) if filtered_service_map else "Google",
                )
                pdf_model = gr.Dropdown(
                    label="模型",
                    choices=["gemma2", "llama2", "mistral", "codellama"],
                    value="gemma2",
                    visible=False
                )
                # 添加环境变量输入框
                # 添加兼容模式复选框
                pdf_compatible_mode = gr.Checkbox(
                    label="兼容模式 (用于非标准PDF文档)",
                    value=False,
                    info="启用此选项可以更好地支持非标准PDF文档的翻译"
                )

                pdf_env_group = gr.Group(visible=True)
                with pdf_env_group:
                    pdf_env1 = gr.Textbox(label="环境变量1", visible=False)
                    pdf_env2 = gr.Checkbox(label="显示思考过程", visible=False)
                    pdf_env3 = gr.Textbox(label="环境变量3", visible=False)
                pdf_translate_btn = gr.Button("开始翻译", variant="primary")

            # 文本处理区域
            with gr.Column(scale=1):
                gr.Markdown("## 文本文件翻译")
                text_file_type = gr.Radio(
                    choices=["File", "Link"],
                    label="上传方式",
                    value="File",
                )
                text_file_input = gr.File(
                    label="选择文本文件",
                    file_count="single",
                    file_types=[".txt", ".md", ".rtf", ".docx", ".epub"],
                    type="filepath",
                    elem_classes=["input-file"],
                )
                text_link_input = gr.Textbox(
                    label="文本链接",
                    visible=False,
                    interactive=True,
                )
                gr.Markdown("### 翻译选项")
                with gr.Row():
                    text_lang_from = gr.Dropdown(
                        label="源语言",
                        choices=lang_map.keys(),
                        value="English",
                    )
                    text_lang_to = gr.Dropdown(
                        label="目标语言",
                        choices=lang_map.keys(),
                        value="Chinese",
                    )
                text_service = gr.Dropdown(
                    label="翻译服务",
                    choices=filtered_service_map.keys(),
                    value=next(iter(filtered_service_map.keys())) if filtered_service_map else "Google",
                )
                text_model = gr.Dropdown(
                    label="模型",
                    choices=["gemma2", "llama2", "mistral", "codellama"],
                    value="gemma2",
                    visible=False
                )
                # 添加环境变量输入框
                text_env_group = gr.Group(visible=True)
                with text_env_group:
                    text_env1 = gr.Textbox(label="环境变量1", visible=False)
                    text_env2 = gr.Checkbox(label="显示思考过程", visible=False)
                    text_env3 = gr.Textbox(label="环境变量3", visible=False)
                text_translate_btn = gr.Button("开始翻译", variant="primary")
            # 页面范围选择（仅用于PDF）
            page_range = gr.Radio(
                choices=page_map.keys(),
                label="页面范围",
                value=list(page_map.keys())[0],
                visible=False,
            )

            def on_select_service(service, evt=None):
                translator = filtered_service_map[service]
                _envs = []

                # 首先将所有环境变量输入框设置为不可见
                for i in range(3):
                    _envs.append(gr.update(visible=False, value=""))

                # 如果是 Gemini 服务，特殊处理
                if service == "Gemini":
                    # 设置 GOOGLE_API_KEY 输入框
                    _envs[0] = gr.update(
                        visible=True,
                        label="GOOGLE_API_KEY",
                        value=os.getenv("GOOGLE_API_KEY", ""),
                        placeholder="请输入您的 Google API Key"
                    )
                    # 设置 GOOGLE_GENAI_THINKING 复选框
                    _envs[1] = gr.update(
                        visible=True,
                        label="显示思考过程",
                        value=os.getenv("GOOGLE_GENAI_THINKING", "false").lower() == "true",
                        interactive=True
                    )
                    # 设置模型选择框
                    gemini_models = ["gemini-1.5-pro", "gemini-1.5-flash"]
                    _envs.append(gr.update(
                        visible=True,
                        choices=gemini_models,
                        value=os.getenv("GOOGLE_GENAI_MODEL", gemini_models[0])
                    ))
                # 如果是 Ollama 服务
                elif service == "Ollama":
                    # 设置环境变量输入框（只显示前三个环境变量）
                    env_items = list(translator.envs.items())
                    for i in range(min(3, len(env_items))):
                        env_name, env_value = env_items[i]
                        # 特殊处理OLLAMA_THINKING_MODEL，显示为复选框
                        if env_name == "OLLAMA_THINKING_MODEL":
                            _envs[i] = gr.update(
                                visible=True,
                                label="思考型模型",
                                value=os.getenv(env_name, env_value).lower() == "true",
                                interactive=True
                            )
                        else:
                            _envs[i] = gr.update(
                                visible=True,
                                label=env_name,
                                value=os.getenv(env_name, env_value)
                            )

                    # 设置模型选择框
                    available_models = OllamaTranslator.get_available_models()
                    # 确保qwen3:14b在可用模型列表中
                    if "qwen3:14b" not in available_models and available_models:
                        available_models.append("qwen3:14b")

                    # 获取当前设置的模型
                    current_model = os.getenv("OLLAMA_MODEL", "gemma2")
                    # 如果当前模型不在可用模型列表中，添加它
                    if current_model not in available_models and current_model:
                        available_models.append(current_model)

                    # 排序模型列表
                    available_models = sorted(available_models)

                    _envs.append(gr.update(
                        visible=True,
                        choices=available_models,
                        value=current_model if current_model in available_models else (available_models[0] if available_models else "gemma2")
                    ))
                # 其他服务
                else:
                    # 设置环境变量输入框
                    for i, env in enumerate(translator.envs.items()):
                        _envs[i] = gr.update(
                            visible=True, label=env[0], value=os.getenv(env[0], env[1])
                        )
                    # 设置模型选择框不可见
                    _envs.append(gr.update(visible=False, value=""))

                return _envs

            def on_select_filetype(file_type):
                return (
                    gr.update(visible=file_type == "File"),
                    gr.update(visible=file_type == "Link"),
                )

            # 添加文件上传事件处理函数
            def on_text_file_upload(file):
                if file:
                    return [
                        gr.update(interactive=True),  # 语言选择
                        gr.update(interactive=True),  # 语言选择
                        gr.update(interactive=True),  # 翻译服务
                        gr.update(interactive=True),  # 翻译按钮
                    ]
                return [
                    gr.update(interactive=False),
                    gr.update(interactive=False),
                    gr.update(interactive=False),
                    gr.update(interactive=False),
                ]

            # 绑定文件上传事件
            text_file_input.change(
                on_text_file_upload,
                inputs=[text_file_input],
                outputs=[
                    text_lang_from,
                    text_lang_to,
                    text_service,
                    text_translate_btn,
                ],
            )

            output_title = gr.Markdown("## Translated", visible=False)
            output_file = gr.File(label="Download Translation", visible=False)
            output_file_dual = gr.File(
                label="Download Translation (Dual)", visible=False
            )
            recaptcha_response = gr.Textbox(
                label="reCAPTCHA Response", elem_id="verify", visible=False
            )
            recaptcha_box = gr.HTML('<div id="recaptcha-box"></div>')
            # Service selection event handler
            # 绑定服务选择事件
            pdf_service.change(
                on_select_service,
                inputs=[pdf_service],
                outputs=[
                    pdf_env1,
                    pdf_env2,
                    pdf_env3,
                    pdf_model,
                ],
            )
            text_service.change(
                on_select_service,
                inputs=[text_service],
                outputs=[
                    text_env1,
                    text_env2,
                    text_env3,
                    text_model,
                ],
            )

            # File type selection event handler
            pdf_file_type.change(
                fn=on_select_filetype,
                inputs=pdf_file_type,
                outputs=[pdf_file_input, pdf_link_input],
            )
            text_file_type.change(
                fn=on_select_filetype,
                inputs=text_file_type,
                outputs=[text_file_input, text_link_input],
            )

            # File upload event handlers
            pdf_file_input.change(
                fn=upload_pdf_file,
                inputs=[pdf_file_input, pdf_service],
                outputs=[pdf_file_input, pdf_preview],
            )
            text_file_input.change(
                fn=upload_text_file,
                inputs=[text_file_input, text_service],
                outputs=[text_file_input],
            )

            # Translation button event handlers
            pdf_translate_btn.click(
                fn=translate_file,
                inputs=[
                    pdf_file_type,
                    pdf_file_input,
                    pdf_link_input,
                    pdf_service,
                    pdf_lang_from,
                    pdf_lang_to,
                    page_range,
                    recaptcha_response,
                    pdf_model,  # 添加模型参数
                    pdf_compatible_mode,  # 添加兼容模式参数
                    pdf_env1,  # 添加环境变量参数
                    pdf_env2,
                    pdf_env3,
                ],
                outputs=[
                    output_file,
                    pdf_preview,
                    output_file_dual,
                    output_title,
                    output_file,
                    output_file_dual,
                ],
            )
            text_translate_btn.click(
                fn=translate_file,
                inputs=[
                    text_file_type,
                    text_file_input,
                    text_link_input,
                    text_service,
                    text_lang_from,
                    text_lang_to,
                    page_range,
                    recaptcha_response,
                    text_model,  # 添加模型参数
                    text_env1,  # 添加环境变量参数
                    text_env2,
                    text_env3,
                ],
                outputs=[
                    output_file,
                    pdf_preview,
                    output_file_dual,
                    output_title,
                    output_file,
                    output_file_dual,
                ],
            )

    try:
        return demo
    except Exception as e:
        print(f"Failed to launch on port {current_port}: {e}")
        return None


def setup_gui(share=False, demo=None, port=7862):
    if demo is None:
        demo = launch_gui()

    if flag_demo:
        demo.launch(server_name="0.0.0.0", max_file_size="5mb", inbrowser=True, server_port=port)
    else:
        try:
            demo.launch(server_name="0.0.0.0", debug=True, inbrowser=True, share=share, server_port=port)
        except Exception:
            print(
                "Error launching GUI using 0.0.0.0.\nThis may be caused by global mode of proxy software."
            )
            try:
                demo.launch(
                    server_name="127.0.0.1", debug=True, inbrowser=True, share=share, server_port=port
                )
            except Exception:
                print(
                    "Error launching GUI using 127.0.0.1.\nThis may be caused by global mode of proxy software."
                )
                demo.launch(debug=True, inbrowser=True, share=True, server_port=port)


# For auto-reloading while developing
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='PDF Math Translate GUI')
    parser.add_argument('--port', type=int, default=7862, help='Port to run the server on')
    args = parser.parse_args()
    setup_gui(port=args.port)
