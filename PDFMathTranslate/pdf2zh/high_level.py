"""Functions that can be used for the most common use-cases for pdf2zh.six"""

from typing import BinaryIO
import numpy as np
import tqdm
import sys
from pymupdf import Font, Document
from pdfminer.pdfpage import PDFPage
from pdfminer.pdfinterp import PDFResourceManager
from pdfminer.pdfdocument import PDFDocument
from pdfminer.pdfparser import PDFParser
from pdfminer.pdfexceptions import PDFValueError
from pdf2zh.converter import TranslateConverter
from pdf2zh.pdfinterp import PDFPageInterpreterEx
from pdf2zh.doclayout import DocLayoutModel
from pathlib import Path
from typing import Any, Container, Iterable, List, Optional
import urllib.request
import requests
import tempfile
import os

model = DocLayoutModel.load_available()

resfont_map = {
    "zh-cn": "china-ss",
    "zh-tw": "china-ts",
    "zh-hans": "china-ss",
    "zh-hant": "china-ts",
    "zh": "china-ss",
    "ja": "japan-s",
    "ko": "korea-s",
}

noto_list = [
    "am",  # Amharic
    "ar",  # Arabic
    "bn",  # Bengali
    "bg",  # Bulgarian
    "chr",  # Cherokee
    "el",  # Greek
    "gu",  # Gujarati
    "iw",  # Hebrew
    "hi",  # Hindi
    # "ja",  # Japanese
    "kn",  # Kannada
    # "ko",  # Korean
    "ml",  # Malayalam
    "mr",  # Marathi
    "ru",  # Russian
    "sr",  # Serbian
    # "zh-cn",# SC
    "ta",  # Tamil
    "te",  # Telugu
    "th",  # Thai
    # "zh-tw",# TC
    "ur",  # Urdu
    "uk",  # Ukrainian
]


def check_files(files: List[str]) -> List[str]:
    files = [
        str(f) for f in files if not str(f).startswith("http://")
    ]  # exclude online files, http
    files = [
        str(f) for f in files if not str(f).startswith("https://")
    ]  # exclude online files, https
    missing_files = [file for file in files if not os.path.exists(file)]
    return missing_files


def translate_patch(
    inf: BinaryIO,
    pages=None,
    password: str = "",
    page_count: int = 0,
    vfont: str = "",
    vchar: str = "",
    thread: int = 0,
    doc_en: Document = None,
    model=None,
    lang_in: str = "",
    lang_out: str = "",
    service: str = "",
    resfont: str = "",
    noto: Font = None,
    callback: object = None,
    **kwarg,
) -> None:
    rsrcmgr = PDFResourceManager()
    layout = {}
    device = TranslateConverter(
        rsrcmgr, vfont, vchar, thread, layout, lang_in, lang_out, service, resfont, noto
    )

    assert device is not None
    obj_patch = {}
    interpreter = PDFPageInterpreterEx(rsrcmgr, device, obj_patch)
    if pages:
        total_pages = len(pages)
    else:
        total_pages = page_count

    parser = PDFParser(inf)
    doc = PDFDocument(parser, password=password)
    with tqdm.tqdm(total=total_pages) as progress:
        for pageno, page in enumerate(PDFPage.create_pages(doc)):
            if pages and (pageno not in pages):
                continue
            progress.update()
            if callback:
                callback(progress)
            page.pageno = pageno
            pix = doc_en[page.pageno].get_pixmap()
            image = np.fromstring(pix.samples, np.uint8).reshape(
                pix.height, pix.width, 3
            )[:, :, ::-1]
            page_layout = model.predict(image, imgsz=int(pix.height / 32) * 32)[0]
            # kdtree 是不可能 kdtree 的，不如直接渲染成图片，用空间换时间
            box = np.ones((pix.height, pix.width))
            h, w = box.shape
            vcls = ["abandon", "figure", "table", "isolate_formula", "formula_caption"]
            for i, d in enumerate(page_layout.boxes):
                if not page_layout.names[int(d.cls)] in vcls:
                    x0, y0, x1, y1 = d.xyxy.squeeze()
                    x0, y0, x1, y1 = (
                        np.clip(int(x0 - 1), 0, w - 1),
                        np.clip(int(h - y1 - 1), 0, h - 1),
                        np.clip(int(x1 + 1), 0, w - 1),
                        np.clip(int(h - y0 + 1), 0, h - 1),
                    )
                    box[y0:y1, x0:x1] = i + 2
            for i, d in enumerate(page_layout.boxes):
                if page_layout.names[int(d.cls)] in vcls:
                    x0, y0, x1, y1 = d.xyxy.squeeze()
                    x0, y0, x1, y1 = (
                        np.clip(int(x0 - 1), 0, w - 1),
                        np.clip(int(h - y1 - 1), 0, h - 1),
                        np.clip(int(x1 + 1), 0, w - 1),
                        np.clip(int(h - y0 + 1), 0, h - 1),
                    )
                    box[y0:y1, x0:x1] = 0
            layout[page.pageno] = box
            # 新建一个 xref 存放新指令流
            page.page_xref = doc_en.get_new_xref()  # hack 插入页面的新 xref
            doc_en.update_object(page.page_xref, "<<>>")
            doc_en.update_stream(page.page_xref, b"")
            doc_en[page.pageno].set_contents(page.page_xref)
            interpreter.process_page(page)

    device.close()
    return obj_patch


def translate(
    files: Iterable[str] = [],
    pages: Optional[Container[int]] = None,
    password: str = "",
    vfont: str = "",
    vchar: str = "",
    thread: int = 0,
    lang_in: str = "",
    lang_out: str = "",
    service: str = "",
    callback: object = None,
    output: str = "",
    compatible: bool = False,  # 添加兼容模式参数
    **kwargs: Any,
):
    from pdf2zh.text_converter import MarkdownConverter, PlainTextConverter, RTFConverter
    from pdf2zh.docx_converter import DocxConverter
    from pdf2zh.translator import GoogleTranslator
    if not files:
        raise PDFValueError("No files to process.")

    missing_files = check_files(files)

    if missing_files:
        print("The following files do not exist:", file=sys.stderr)
        for file in missing_files:
            print(f"  {file}", file=sys.stderr)
        raise PDFValueError("Some files do not exist.")

    for file in files:
        if file is str and (file.startswith("http://") or file.startswith("https://")):
            print("Online files detected, downloading...")
            try:
                r = requests.get(file, allow_redirects=True)
                if r.status_code == 200:
                    if not os.path.exists("./pdf2zh_files"):
                        print("Making a temporary dir for downloading PDF files...")
                        os.mkdir(os.path.dirname("./pdf2zh_files"))
                    with open("./pdf2zh_files/tmp_download.pdf", "wb") as f:
                        print(f"Writing the file: {file}...")
                        f.write(r.content)
                    file = "./pdf2zh_files/tmp_download.pdf"
                else:
                    r.raise_for_status()
            except Exception as e:
                raise PDFValueError(
                    f"Errors occur in downloading the PDF file. Please check the link(s).\nError:\n{e}"
                )

        file_path = Path(file)
        filename = file_path.stem
        extension = file_path.suffix.lower()

        # 处理EPUB文件（独立处理，不依赖PDFMathTranslate核心功能）
        if extension == ".epub":
            from pdf2zh.epub_translator import translate_epub_file

            input_path = str(file_path)
            output_path = str(Path(output) / f"{filename}-{lang_out}.epub")

            # 使用独立的EPUB翻译器
            success = translate_epub_file(
                input_path=input_path,
                output_path=output_path,
                translator_service=service,
                lang_in=lang_in,
                lang_out=lang_out,
                progress_callback=callback
            )

            if not success:
                raise ValueError(f"EPUB translation failed for {file_path}")

            continue

        # 处理文本文件（Markdown、TXT、RTF、DOCX）
        if extension in [".md", ".txt", ".rtf", ".docx"]:
            from pdf2zh.translator import service_map
            translator_class = service_map.get(service, "google")
            translator = translator_class(service=service, lang_in=lang_in, lang_out=lang_out, model=None)

            if extension == ".md":
                converter = MarkdownConverter(translator)
            elif extension == ".txt":
                converter = PlainTextConverter(translator)
            elif extension == ".rtf":
                converter = RTFConverter(translator)
            elif extension == ".docx":
                converter = DocxConverter(translator)

            content = converter.extract_text(file_path)
            # 创建tqdm对象用于文本翻译进度
            with tqdm.tqdm(total=len(content)) as progress:
                translated_content = []
                for i, line in enumerate(content):
                    translated_line = converter.translate_content([line])
                    translated_content.extend(translated_line)
                    progress.update(1)
                    if callback:
                        callback(progress)
            output_path = Path(output) / f"{filename}-{lang_out}{extension}"
            converter.save_translated(translated_content, output_path)
            continue

        # 处理PDF文件
        filename = os.path.splitext(os.path.basename(file))[0]

        font_list = [("tiro", None)]
        noto = None
        if lang_out.lower() in resfont_map:  # CJK
            resfont = resfont_map[lang_out.lower()]
            font_list.append((resfont, None))
        elif lang_out.lower() in noto_list:  # noto
            resfont = "noto"
            ttf_path = os.path.join(tempfile.gettempdir(), "GoNotoKurrent-Regular.ttf")
            if not os.path.exists(ttf_path):
                print("Downloading Noto font...")
                urllib.request.urlretrieve(
                    "https://github.com/satbyy/go-noto-universal/releases/download/v7.0/GoNotoKurrent-Regular.ttf",
                    ttf_path,
                )
            font_list.append(("noto", ttf_path))
            noto = Font("noto", ttf_path)
        else:  # fallback
            resfont = "china-ss"
            font_list.append(("china-ss", None))

        doc_en = Document(file)
        page_count = doc_en.page_count
        # font_list = [("china-ss", None), ("tiro", None)]
        font_id = {}
        for page in doc_en:
            for font in font_list:
                font_id[font[0]] = page.insert_font(font[0], font[1])
        xreflen = doc_en.xref_length()
        for xref in range(1, xreflen):
            for label in ["Resources/", ""]:  # 可能是基于 xobj 的 res
                try:  # xref 读写可能出错
                    font_res = doc_en.xref_get_key(xref, f"{label}Font")
                    if font_res[0] == "dict":
                        for font in font_list:
                            font_exist = doc_en.xref_get_key(
                                xref, f"{label}Font/{font[0]}"
                            )
                            if font_exist[0] == "null":
                                doc_en.xref_set_key(
                                    xref,
                                    f"{label}Font/{font[0]}",
                                    f"{font_id[font[0]]} 0 R",
                                )
                except Exception:
                    pass
        doc_en.save(Path(output) / f"{filename}-en.pdf")

        with open(Path(output) / f"{filename}-en.pdf", "rb") as fp:
            obj_patch: dict = translate_patch(fp, model=model, **locals())

        for obj_id, ops_new in obj_patch.items():
            # ops_old=doc_en.xref_stream(obj_id)
            # print(obj_id)
            # print(ops_old)
            # print(ops_new.encode())
            doc_en.update_stream(obj_id, ops_new.encode())

        doc_zh = doc_en
        doc_dual = Document(Path(output) / f"{filename}-en.pdf")
        doc_dual.insert_file(doc_zh)
        for id in range(page_count):
            doc_dual.move_page(page_count + id, id * 2 + 1)

        # 根据是否启用兼容模式选择不同的保存参数
        if compatible:
            # 兼容模式：不使用PDF/A格式，适用于非标准PDF文档
            print("Using compatibility mode for saving PDF...")
            doc_zh.save(
                Path(output) / f"{filename}-zh.pdf",
                deflate=1,
                garbage=3,  # 更高级别的垃圾收集
                clean=True,  # 清理未使用的对象
                pretty=False,  # 不美化输出
                ascii=False,  # 允许非ASCII字符
                linear=False  # 非线性PDF
            )
            doc_dual.save(
                Path(output) / f"{filename}-dual.pdf",
                deflate=1,
                garbage=3,
                clean=True,
                pretty=False,
                ascii=False,
                linear=False
            )
        else:
            # 标准模式：使用PDF/A格式
            doc_zh.save(Path(output) / f"{filename}-zh.pdf", deflate=1)
            doc_dual.save(Path(output) / f"{filename}-dual.pdf", deflate=1)

        doc_zh.close()
        doc_dual.close()
        os.remove(Path(output) / f"{filename}-en.pdf")

    return
